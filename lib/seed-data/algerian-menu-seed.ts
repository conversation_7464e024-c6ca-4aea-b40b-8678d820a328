import { v4 as uuidv4 } from 'uuid';
import { PRESET_COLORS } from '../constants';

// Helper function to create unique IDs
const createId = () => uuidv4();

// 🍕 UNIVERSAL SEED - Optimized for Algerian Pizzeria Market
// All names in French, prices in Algerian Dinars (DA)

// Define pizza sizes with Algerian market standards
export const pizzaSizes = ['Normale', 'Mega', 'Géante'];

// Define drink sizes
export const drinkSizes = ['33cl', '50cl', '1.5L'];

// Define sandwich sizes  
export const sandwichSizes = ['Simple', 'Double'];

// Define supplement pricing by size
export const supplementPricing = {
  'Normale': { base: 50, premium: 80 },
  'Mega': { base: 75, premium: 120 },
  'Géante': { base: 100, premium: 150 }
};

// 🍕 COMPREHENSIVE ALGERIAN PIZZERIA MENU
export const algerianMenu = [
  {
    id: createId(),
    name: 'Pizzas Classiques',
    emoji: '🍕',
    color: PRESET_COLORS[0],
    sizes: pizzaSizes,
    items: [
      {
        id: createId(),
        name: '<PERSON><PERSON><PERSON><PERSON>',
        description: 'Sauce tomate, mozzarella, basilic frais',
        prices: {
          'Normale': 450,
          'Mega': 650,
          'Géante': 850
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Quatre Fromages',
        description: 'Mozzarella, emmental, chèvre, bleu',
        prices: {
          'Normale': 600,
          'Mega': 800,
          'Géante': 1000
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Végétarienne',
        description: 'Poivrons, champignons, olives, tomates',
        prices: {
          'Normale': 500,
          'Mega': 700,
          'Géante': 900
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Regina',
        description: 'Jambon, champignons, mozzarella',
        prices: {
          'Normale': 550,
          'Mega': 750,
          'Géante': 950
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Pepperoni',
        description: 'Pepperoni, mozzarella, sauce tomate',
        prices: {
          'Normale': 600,
          'Mega': 800,
          'Géante': 1000
        },
        color: PRESET_COLORS[4]
      }
    ],
    // Pizza supplements configuration
    supplements: [
      {
        id: createId(),
        name: 'Extra Fromage',
        description: 'Double portion de fromage',
        isActive: true
      },
      {
        id: createId(),
        name: 'Olives Noires',
        description: 'Olives noires de Kalamata',
        isActive: true
      },
      {
        id: createId(),
        name: 'Champignons',
        description: 'Champignons de Paris frais',
        isActive: true
      },
      {
        id: createId(),
        name: 'Poivrons',
        description: 'Poivrons colorés grillés',
        isActive: true
      }
    ],
    supplementConfig: {
      globalPricing: {
        'Normale': 50,
        'Mega': 75,
        'Géante': 100
      },
      isEnabled: true
    }
  },
  {
    id: createId(),
    name: 'Pizzas Spéciales',
    emoji: '🌟',
    color: PRESET_COLORS[1],
    sizes: pizzaSizes,
    items: [
      {
        id: createId(),
        name: 'Algérienne',
        description: 'Merguez, poivrons, harissa, mozzarella',
        prices: {
          'Normale': 650,
          'Mega': 850,
          'Géante': 1050
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Orientale',
        description: 'Poulet mariné, olives, tomates, épices',
        prices: {
          'Normale': 600,
          'Mega': 800,
          'Géante': 1000
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Royal',
        description: 'Viande hachée, merguez, poulet, fromages',
        prices: {
          'Normale': 700,
          'Mega': 900,
          'Géante': 1100
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Méditerranéenne',
        description: 'Thon, anchois, olives, câpres, tomates',
        prices: {
          'Normale': 580,
          'Mega': 780,
          'Géante': 980
        },
        color: PRESET_COLORS[3]
      }
    ]
  },
  {
    id: createId(),
    name: 'Sandwiches & Wraps',
    emoji: '🥪',
    color: PRESET_COLORS[2],
    sizes: sandwichSizes,
    items: [
      {
        id: createId(),
        name: 'Kebab Traditionnel',
        description: 'Viande d\'agneau, salade, tomates, sauce',
        prices: {
          'Simple': 350,
          'Double': 500
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Poulet Grillé',
        description: 'Escalope de poulet, crudités, sauce algérienne',
        prices: {
          'Simple': 380,
          'Double': 550
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Merguez',
        description: 'Merguez grillées, frites, harissa',
        prices: {
          'Simple': 320,
          'Double': 480
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Kefta Maison',
        description: 'Boulettes de viande épicées, légumes',
        prices: {
          'Simple': 400,
          'Double': 580
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Thon Mayo',
        description: 'Thon, mayonnaise, œuf dur, salade',
        prices: {
          'Simple': 280,
          'Double': 420
        },
        color: PRESET_COLORS[4]
      }
    ]
  },
  {
    id: createId(),
    name: 'Boissons',
    emoji: '🥤',
    color: PRESET_COLORS[3],
    sizes: drinkSizes,
    items: [
      {
        id: createId(),
        name: 'Coca-Cola',
        description: 'Boisson gazeuse classique',
        prices: {
          '33cl': 120,
          '50cl': 180,
          '1.5L': 350
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Fanta Orange',
        description: 'Boisson gazeuse à l\'orange',
        prices: {
          '33cl': 120,
          '50cl': 180,
          '1.5L': 350
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Sprite',
        description: 'Boisson gazeuse citron-lime',
        prices: {
          '33cl': 120,
          '50cl': 180,
          '1.5L': 350
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Eau Minérale',
        description: 'Eau minérale naturelle',
        prices: {
          '33cl': 80,
          '50cl': 120,
          '1.5L': 200
        },
        color: PRESET_COLORS[3]
      },
      {
        id: createId(),
        name: 'Thé à la Menthe',
        description: 'Thé traditionnel algérien',
        prices: {
          '33cl': 100,
          '50cl': 150,
          '1.5L': 280
        },
        color: PRESET_COLORS[4]
      },
      {
        id: createId(),
        name: 'Jus d\'Orange Frais',
        description: 'Jus d\'orange pressé maison',
        prices: {
          '33cl': 200,
          '50cl': 300,
          '1.5L': 500
        },
        color: PRESET_COLORS[0]
      }
    ]
  },
  {
    id: createId(),
    name: 'Desserts',
    emoji: '🍰',
    color: PRESET_COLORS[4],
    items: [
      {
        id: createId(),
        name: 'Baklawa',
        description: 'Pâtisserie orientale au miel et pistaches',
        prices: {
          'Portion': 250
        },
        color: PRESET_COLORS[0]
      },
      {
        id: createId(),
        name: 'Makroudh',
        description: 'Gâteau de semoule aux dattes',
        prices: {
          'Portion': 200
        },
        color: PRESET_COLORS[1]
      },
      {
        id: createId(),
        name: 'Tiramisu',
        description: 'Dessert italien au café',
        prices: {
          'Portion': 300
        },
        color: PRESET_COLORS[2]
      },
      {
        id: createId(),
        name: 'Glace Vanille',
        description: 'Boules de glace à la vanille',
        prices: {
          '2 Boules': 180,
          '3 Boules': 250
        },
        color: PRESET_COLORS[3]
      }
    ]
  }
];
