// knowledge: universal v4 seed for suppliers, inventory, menu, sub-recipes, and menu item recipes
import { addSupplier } from '../db/v4/operations/supplier-ops';
import { addStockItem } from '../db/v4/operations/inventory-ops';
import { addCategory, updateMenu, getMenu } from '../db/v4/operations/menu-ops';
import { createSubRecipe } from '../db/v4/operations/sub-recipe-ops';
import { createMenuItemRecipe } from '../db/v4/operations/menu-item-recipe-ops';
import { createSupplement, updateCategorySupplementConfig } from '../db/v4/operations/supplement-ops';
import { updateCategoryPackaging } from '../db/v4/operations/packaging-ops';
import { ensureDefaultSettings } from '../db/v4/operations/settings-ops';
import { RestaurantSettings } from '../db/v4/schemas/restaurant-settings-schema';
import { Supplement, MenuCategory } from '../db/v4/schemas/menu-schema';
import { getRandomPresetColor, PRESET_COLORS } from '../constants';
import { algerianMenu } from './algerian-menu-seed';
import { v4 as uuidv4 } from 'uuid';

// Minimal, real, and consistent data for a pizza restaurant
export async function runUniversalSeedV4() {
  // knowledge: wrap all in try/catch for safety
  try {
    console.log('🚀 Starting Universal Seed V4...');

    // --- RESTAURANT SETTINGS ---
    console.log('⏳ Ensuring default restaurant settings...');
    await ensureDefaultSettings();
    console.log('✅ Default restaurant settings ensured.');

    // --- SUPPLIERS ---
    console.log('⏳ Seeding suppliers...');
    const now = new Date().toISOString();
    const suppliers = [
      {
        name: 'Boulangerie Omar',
        phoneNumber: '0550123456',
        category: 'Boulangerie',
        notes: 'Pain traditionnel, semoule et farines',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Fromagerie Djurdjura',
        phoneNumber: '0550998877',
        category: 'Produits laitiers',
        notes: 'Fromages locaux et importés',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Grossiste Boissons El Bahdja',
        phoneNumber: '0771987654',
        category: 'Boissons',
        notes: 'Boissons gazeuses et eaux minérales',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Épicerie Atlas',
        phoneNumber: '0661234567',
        category: 'Épices et condiments',
        notes: 'Épices traditionnelles algériennes et orientales',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Boucherie Halal El Baraka',
        phoneNumber: '0772345678',
        category: 'Viandes',
        notes: 'Viandes fraîches halal de qualité',
        balance: 0,
        isActive: true,
      },
      {
        name: 'Marché des Légumes Frais',
        phoneNumber: '0553456789',
        category: 'Légumes et fruits',
        notes: 'Légumes et fruits frais du marché local',
        balance: 0,
        isActive: true,
      },
    ];
    
    const supplierIds: Record<string, string> = {};
    for (const s of suppliers) {
      const created = await addSupplier(s);
      supplierIds[s.name] = created.id;
    }
    console.log('✅ Suppliers seeded.');

    // --- PIZZERIA INVENTORY ---
    console.log('⏳ Seeding pizzeria inventory...');
    // Essential pizza restaurant ingredients
    const stockItems = [
      // === PIZZA DOUGH ESSENTIALS ===
      {
        name: 'Farine Pizza T00',
        category: 'Ingrédients Pizza',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 50,
        minLevel: 10,
        costPerUnit: 180,
        purchaseUnits: [
          {
            id: `pu_${uuidv4()}`,
            name: 'Sac 25kg',
            conversionToBase: 25,
            isDefault: true,
            createdAt: now,
            updatedAt: now
          }
        ]
      },
      {
        name: 'Levure Fraîche',
        category: 'Ingrédients Pizza',
        unit: 'kg' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 2,
        minLevel: 0.5,
        costPerUnit: 800,
      },
      {
        name: 'Sel Fin',
        category: 'Ingrédients Pizza',
        unit: 'kg' as const,
        supplierId: supplierIds['Épicerie Atlas'],
        quantity: 5,
        minLevel: 1,
        costPerUnit: 60,
      },
      {
        name: "Huile d'Olive Extra Vierge",
        category: 'Ingrédients Pizza',
        unit: 'L' as const,
        supplierId: supplierIds['Épicerie Atlas'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 450,
      },

      // === FROMAGES PIZZA ===
      {
        name: 'Mozzarella Râpée',
        category: 'Fromages',
        unit: 'kg' as const,
        supplierId: supplierIds['Fromagerie Djurdjura'],
        quantity: 30,
        minLevel: 8,
        costPerUnit: 650,
      },
      {
        name: 'Fromage Cheddar',
        category: 'Fromages',
        unit: 'kg' as const,
        supplierId: supplierIds['Fromagerie Djurdjura'],
        quantity: 15,
        minLevel: 3,
        costPerUnit: 750,
      },
      {
        name: 'Fromage Gruyère',
        category: 'Fromages',
        unit: 'kg' as const,
        supplierId: supplierIds['Fromagerie Djurdjura'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 900,
      },

      // === GARNITURES PIZZA ===
      {
        name: 'Sauce Tomate Pizza',
        category: 'Sauces',
        unit: 'kg' as const,
        supplierId: supplierIds['Épicerie Atlas'],
        quantity: 20,
        minLevel: 5,
        costPerUnit: 180,
      },
      {
        name: 'Pepperoni',
        category: 'Charcuterie',
        unit: 'kg' as const,
        supplierId: supplierIds['Boucherie Halal El Baraka'],
        quantity: 8,
        minLevel: 2,
        costPerUnit: 1200,
      },
      {
        name: 'Merguez Tranchées',
        category: 'Charcuterie',
        unit: 'kg' as const,
        supplierId: supplierIds['Boucherie Halal El Baraka'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 900,
      },
      {
        name: 'Poulet Grillé Émincé',
        category: 'Viandes',
        unit: 'kg' as const,
        supplierId: supplierIds['Boucherie Halal El Baraka'],
        quantity: 15,
        minLevel: 3,
        costPerUnit: 1200,
      },

      // === LÉGUMES PIZZA ===
      {
        name: 'Champignons de Paris',
        category: 'Légumes',
        unit: 'kg' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 8,
        minLevel: 2,
        costPerUnit: 350,
      },
      {
        name: 'Poivrons Rouges',
        category: 'Légumes',
        unit: 'kg' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 10,
        minLevel: 2,
        costPerUnit: 280,
      },
      {
        name: 'Oignons Rouges',
        category: 'Légumes',
        unit: 'kg' as const,
        supplierId: supplierIds['Marché des Légumes Frais'],
        quantity: 12,
        minLevel: 3,
        costPerUnit: 150,
      },
      {
        name: 'Olives Noires',
        category: 'Légumes',
        unit: 'kg' as const,
        supplierId: supplierIds['Épicerie Atlas'],
        quantity: 5,
        minLevel: 1,
        costPerUnit: 450,
      },

      // === BOISSONS PIZZERIA ===
      {
        name: 'Coca-Cola 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 48,
        minLevel: 12,
        costPerUnit: 60,
      },
      {
        name: 'Fanta Orange 33cl',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 24,
        minLevel: 6,
        costPerUnit: 55,
      },
      {
        name: 'Eau Minérale 0.5L',
        category: 'Boissons',
        unit: 'pcs' as const,
        supplierId: supplierIds['Grossiste Boissons El Bahdja'],
        quantity: 48,
        minLevel: 12,
        costPerUnit: 25,
      },

      // === EMBALLAGE PIZZERIA ===
      {
        name: 'Boîtes Pizza 30cm',
        category: 'Emballages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 200,
        minLevel: 50,
        costPerUnit: 18,
      },
      {
        name: 'Boîtes Pizza 35cm',
        category: 'Emballages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 150,
        minLevel: 30,
        costPerUnit: 22,
      },
      {
        name: 'Boîtes Pizza Standard',
        category: 'Emballages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 100,
        minLevel: 20,
        costPerUnit: 25,
      },
      {
        name: 'Serviettes Pizza',
        category: 'Emballages',
        unit: 'pcs' as const,
        supplierId: supplierIds['Boulangerie Omar'],
        quantity: 1000,
        minLevel: 200,
        costPerUnit: 0.8,
      },
    ];
    
    const stockItemIds: Record<string, string> = {};
    for (const s of stockItems) {
      const created = await addStockItem(s);
      stockItemIds[s.name] = created.id;
    }
    console.log('✅ Inventory items seeded.');

    // --- RECETTES PIZZERIA ---
    console.log('⏳ Seeding pizza recipes...');
    const subRecipes = [
      {
        name: 'Pâte à Pizza',
        ingredients: [
          { stockItemId: stockItemIds['Farine Pizza T00'], quantity: 1 },
          { stockItemId: stockItemIds['Levure Fraîche'], quantity: 0.02 },
          { stockItemId: stockItemIds['Sel Fin'], quantity: 0.02 },
          { stockItemId: stockItemIds["Huile d'Olive Extra Vierge"], quantity: 0.05 },
        ],
        costPerUnit: 0,
        unit: 'kg' as const,
        yield: { quantity: 1.2, unit: 'kg' as const },
      },
      {
        name: 'Base Pizza Margherita',
        ingredients: [
          { stockItemId: stockItemIds['Sauce Tomate Pizza'], quantity: 0.15 },
          { stockItemId: stockItemIds['Mozzarella Râpée'], quantity: 0.2 },
        ],
        costPerUnit: 0,
        unit: 'kg' as const,
        yield: { quantity: 0.35, unit: 'kg' as const },
      },
    ];
    
    const subRecipeIds: Record<string, string> = {};
    for (const sr of subRecipes) {
      const created = await createSubRecipe(sr);
      subRecipeIds[sr.name] = created._id;
    }
    console.log('✅ Sub-recipes seeded.');

    // --- COMPREHENSIVE ALGERIAN MENU ---
    console.log('⏳ Seeding comprehensive Algerian pizzeria menu...');
    
    // Use the enhanced Algerian menu from algerian-menu-seed.ts
    const menuCategoryIds: Record<string, string> = {};
    for (const category of algerianMenu) {
      await addCategory(category as MenuCategory);
      menuCategoryIds[category.name] = category.id;
      console.log(`✅ Category '${category.name}' with ${category.items.length} items created.`);
    }

    console.log('✅ Comprehensive Algerian menu seeded successfully!');
    console.log(`📊 Total categories: ${algerianMenu.length}`);
    console.log(`📊 Total items: ${algerianMenu.reduce((total, cat) => total + cat.items.length, 0)}`);

    // --- MENU ITEM RECIPES ---
    console.log('⏳ Seeding menu item recipes...');
    
    // Find pizza categories and items
    const pizzaClassiques = algerianMenu.find(cat => cat.name === 'Pizzas Classiques');
    const pizzaSpeciales = algerianMenu.find(cat => cat.name === 'Pizzas Spéciales');
    
    // Pizza Margherita recipe
    const margheritaItem = pizzaClassiques?.items.find(item => item.name === 'Margherita');
    if (margheritaItem) {
      await createMenuItemRecipe({
        menuItemId: margheritaItem.id,
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza'], quantity: 0.3 },
          { subRecipeId: subRecipeIds['Base Pizza Margherita'], quantity: 1 },
        ],
      });
    }

    // Pizza Royal recipe (from Pizzas Spéciales)
    const royalItem = pizzaSpeciales?.items.find(item => item.name === 'Royal');
    if (royalItem) {
      await createMenuItemRecipe({
        menuItemId: royalItem.id,
        ingredients: [
          { subRecipeId: subRecipeIds['Pâte à Pizza'], quantity: 0.3 },
          { subRecipeId: subRecipeIds['Base Pizza Margherita'], quantity: 1 },
          { stockItemId: stockItemIds['Merguez Tranchées'], quantity: 0.1 },
          { stockItemId: stockItemIds['Poulet Grillé Émincé'], quantity: 0.1 },
          { stockItemId: stockItemIds['Pepperoni'], quantity: 0.05 },
        ],
      });
    }

    console.log('✅ Menu item recipes seeded.');

    // --- PACKAGING CONFIGURATION ---
    console.log('⏳ Configuring packaging rules...');
    
    // Configure packaging for Pizza categories
    const pizzaClassiquesCategory = algerianMenu.find(cat => cat.name === 'Pizzas Classiques');
    const pizzaSpecialesCategory = algerianMenu.find(cat => cat.name === 'Pizzas Spéciales');
    
    const pizzaCategories = [pizzaClassiquesCategory, pizzaSpecialesCategory].filter(Boolean);
    
    for (const pizzaCategory of pizzaCategories) {
      const pizzaPackagingRules = {
        'Normale': {
          'dine-in': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 2 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Boîtes Pizza 30cm'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 2 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîtes Pizza 30cm'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 3 }
          ]
        },
        'Mega': {
          'dine-in': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 3 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Boîtes Pizza 35cm'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 3 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîtes Pizza 35cm'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 4 }
          ]
        },
        'Géante': {
          'dine-in': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 4 }
          ],
          'takeaway': [
            { stockItemId: stockItemIds['Boîtes Pizza Standard'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 4 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Boîtes Pizza Standard'], quantity: 1 },
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 5 }
          ]
        }
      };

      // Apply pizza packaging rules
      for (const [size, orderTypes] of Object.entries(pizzaPackagingRules)) {
        for (const [orderType, packaging] of Object.entries(orderTypes)) {
          await updateCategoryPackaging(pizzaCategory.id, size, orderType as any, packaging);
        }
      }
    }

    // Configure packaging for Drinks category
    const drinksCategory = algerianMenu.find(cat => cat.name === 'Boissons');
    if (drinksCategory) {
      const drinksPackagingRules = {
        '33cl': {
          'takeaway': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 1 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 1 }
          ]
        },
        '50cl': {
          'takeaway': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 1 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 1 }
          ]
        },
        '1.5L': {
          'takeaway': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 2 }
          ],
          'delivery': [
            { stockItemId: stockItemIds['Serviettes Pizza'], quantity: 2 }
          ]
        }
      };

      // Apply drinks packaging rules
      for (const [size, orderTypes] of Object.entries(drinksPackagingRules)) {
        for (const [orderType, packaging] of Object.entries(orderTypes)) {
          await updateCategoryPackaging(drinksCategory.id, size, orderType as any, packaging);
        }
      }
    }
    console.log('✅ Packaging rules configured.');

    // --- CATEGORY-SPECIFIC SUPPLEMENTS ---
    console.log('⏳ Seeding category-specific supplements...');
    
    // Pizza supplements
    const pizzaSupplements: Supplement[] = [
      {
        id: `sup_${uuidv4()}`,
        name: 'Extra Fromage',
        description: 'Double portion de fromage',
        stockConsumption: {
          stockItemId: stockItemIds['Mozzarella Râpée'],
          quantities: { Normale: 0.05, Mega: 0.08, Géante: 0.1 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Fromage Cheddar',
        description: 'Ajout de cheddar',
        stockConsumption: {
          stockItemId: stockItemIds['Fromage Cheddar'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Fromage Gruyère',
        description: 'Ajout de gruyère',
        stockConsumption: {
          stockItemId: stockItemIds['Fromage Gruyère'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Olives',
        description: 'Ajout d\'olives noires',
        stockConsumption: {
          stockItemId: stockItemIds['Olives Noires'],
          quantities: { Normale: 0.02, Mega: 0.03, Géante: 0.05 },
        },
        isActive: true,
      },
      {
        id: `sup_${uuidv4()}`,
        name: 'Champignons',
        description: 'Ajout de champignons de Paris',
        stockConsumption: {
          stockItemId: stockItemIds['Champignons de Paris'],
          quantities: { Normale: 0.03, Mega: 0.05, Géante: 0.07 },
        },
        isActive: true,
      },
    ];

    // Create supplements for Pizza categories
    const pizzaClassiquesId = menuCategoryIds['Pizzas Classiques'];
    const pizzaSpecialesId = menuCategoryIds['Pizzas Spéciales'];
    
    const pizzaCategoryIds = [pizzaClassiquesId, pizzaSpecialesId].filter(Boolean);
    
    for (const pizzaCategoryId of pizzaCategoryIds) {
      for (const supplement of pizzaSupplements) {
        await createSupplement(pizzaCategoryId, supplement);
      }
      
      // Configure pricing for Pizza supplements
      await updateCategorySupplementConfig(pizzaCategoryId, {
        globalPricing: { Normale: 100, Mega: 150, Géante: 200 },
        isEnabled: true,
      });
    }

    // Drinks supplements (different supplements for drinks)
    const drinkSupplements: Supplement[] = [
      {
        id: `sup_${uuidv4()}`,
        name: 'Glaçons Extra',
        description: 'Portion supplémentaire de glaçons',
        stockConsumption: {
          stockItemId: stockItemIds['Eau Minérale 0.5L'],
          quantities: { '33cl': 0.05, '0.5L': 0.1 },
        },
        isActive: true,
      },
    ];

    // Create supplements for Drinks category
    const drinksCategoryId = menuCategoryIds['Boissons'];
    if (drinksCategoryId) {
      for (const supplement of drinkSupplements) {
        await createSupplement(drinksCategoryId, supplement);
      }
      
      // Configure pricing for Drinks supplements (different pricing structure)
      await updateCategorySupplementConfig(drinksCategoryId, {
        globalPricing: { '33cl': 10, '50cl': 15, '1.5L': 30 },
        isEnabled: true,
      });
    }

    console.log('✅ Category-specific supplements seeded and configured.');

    console.log('🎉 Universal seed V4 completed successfully!');
  } catch (error) {
    console.error('❌ Error running universal seed V4:', error);
    throw error;
  }
}
