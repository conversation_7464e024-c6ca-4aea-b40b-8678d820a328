/**
 * Autonomous Sync Manager
 * 
 * Fully autonomous background sync system that:
 * - Starts automatically when app launches
 * - Continuously discovers CouchDB servers
 * - Maintains persistent connections
 * - Auto-reconnects on failures
 * - Runs completely in the background
 */

import { discoverCouchDBServers } from './ip-discovery';
import { nativeSyncService, type SyncServer, type SyncStatus } from './native-sync';

// Extend SyncServer interface for autonomous sync
interface EnhancedSyncServer extends SyncServer {
  responseTime?: number;
  lastSeen?: Date;
}
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';

interface AutonomousConfig {
  discoveryInterval: number;      // How often to scan for servers (ms)
  reconnectInterval: number;      // How often to retry failed connections (ms)
  maxReconnectAttempts: number;   // Max reconnection attempts before giving up
  autoStart: boolean;             // Start automatically on initialization
  preferredServers: string[];     // Preferred server IPs to connect to first
}

interface AutonomousStatus {
  isRunning: boolean;
  isDiscovering: boolean;
  lastDiscovery: Date | null;
  discoveredServers: EnhancedSyncServer[];
  connectedServers: EnhancedSyncServer[];
  failedServers: { server: EnhancedSyncServer; attempts: number; lastAttempt: Date }[];
  syncStatus: SyncStatus;
}

class AutonomousSyncManager {
  private config: AutonomousConfig = {
    discoveryInterval: 30000,      // 30 seconds
    reconnectInterval: 60000,      // 1 minute
    maxReconnectAttempts: 5,
    autoStart: true,
    preferredServers: []
  };

  private status: AutonomousStatus = {
    isRunning: false,
    isDiscovering: false,
    lastDiscovery: null,
    discoveredServers: [],
    connectedServers: [],
    failedServers: [],
    syncStatus: nativeSyncService.getStatus()
  };

  private discoveryTimer: NodeJS.Timeout | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private listeners: ((status: AutonomousStatus) => void)[] = [];
  private isInitialized = false;

  constructor() {
    // Listen to native sync status changes
    nativeSyncService.onStatusChange((syncStatus) => {
      this.status.syncStatus = syncStatus;
      this.emitStatusUpdate();
    });

    // Listen to database initialization
    if (typeof window !== 'undefined') {
      document.addEventListener('v4-pouchdb-initialized', this.handleDatabaseReady.bind(this));
    }
  }

  /**
   * Initialize the autonomous sync manager
   */
  async initialize(config?: Partial<AutonomousConfig>): Promise<void> {
    if (this.isInitialized) {
      console.log('🤖 [AutonomousSync] Already initialized');
      return;
    }

    console.log('🤖 [AutonomousSync] Initializing autonomous sync manager...');
    
    // Merge config
    this.config = { ...this.config, ...config };
    this.isInitialized = true;

    // Auto-start if enabled and database is ready
    if (this.config.autoStart) {
      await this.waitForDatabaseAndStart();
    }

    console.log('🤖 [AutonomousSync] Initialized with config:', this.config);
  }

  /**
   * Start autonomous sync operations
   */
  async start(): Promise<void> {
    if (this.status.isRunning) {
      console.log('🤖 [AutonomousSync] Already running');
      return;
    }

    console.log('🤖 [AutonomousSync] Starting autonomous sync...');
    this.status.isRunning = true;
    this.emitStatusUpdate();

    // Start initial discovery
    await this.performDiscovery();

    // Start periodic discovery
    this.startPeriodicDiscovery();

    // Start reconnection monitoring
    this.startReconnectionMonitoring();

    console.log('🤖 [AutonomousSync] Started successfully');
  }

  /**
   * Stop autonomous sync operations
   */
  async stop(): Promise<void> {
    console.log('🤖 [AutonomousSync] Stopping autonomous sync...');
    
    this.status.isRunning = false;
    
    // Clear timers
    if (this.discoveryTimer) {
      clearInterval(this.discoveryTimer);
      this.discoveryTimer = null;
    }
    
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Disconnect from all servers
    await nativeSyncService.stopSync();
    
    this.status.connectedServers = [];
    this.emitStatusUpdate();

    console.log('🤖 [AutonomousSync] Stopped');
  }

  /**
   * Get current status
   */
  getStatus(): AutonomousStatus {
    return { ...this.status };
  }

  /**
   * Subscribe to status changes
   */
  onStatusChange(listener: (status: AutonomousStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  /**
   * Manual discovery trigger
   */
  async discover(): Promise<EnhancedSyncServer[]> {
    return await this.performDiscovery();
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<AutonomousConfig>): void {
    this.config = { ...this.config, ...config };
    console.log('🤖 [AutonomousSync] Config updated:', this.config);
    
    // Restart timers with new intervals if running
    if (this.status.isRunning) {
      this.restartTimers();
    }
  }

  // Private methods

  private async waitForDatabaseAndStart(): Promise<void> {
    console.log('🤖 [AutonomousSync] Waiting for database initialization...');
    
    // Wait for database to be ready
    const maxWait = 30000; // 30 seconds
    const startTime = Date.now();
    
    while (!mainDbInstance.isInitialized && (Date.now() - startTime) < maxWait) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    if (mainDbInstance.isInitialized) {
      console.log('🤖 [AutonomousSync] Database ready, starting autonomous sync');
      await this.start();
    } else {
      console.warn('🤖 [AutonomousSync] Database initialization timeout, will start when ready');
    }
  }

  private handleDatabaseReady = async (event: any) => {
    if (event.detail?.success && this.config.autoStart && !this.status.isRunning) {
      console.log('🤖 [AutonomousSync] Database initialized, starting autonomous sync');
      await this.start();
    }
  };

  private async performDiscovery(): Promise<EnhancedSyncServer[]> {
    if (this.status.isDiscovering) {
      console.log('🤖 [AutonomousSync] Discovery already in progress');
      return this.status.discoveredServers;
    }

    console.log('🤖 [AutonomousSync] Starting server discovery...');
    this.status.isDiscovering = true;
    this.emitStatusUpdate();

    try {
      // Use enhanced discovery with caching
      const discovered = await discoverCouchDBServers({
        timeout: 2000,  // Faster timeout since we have caching
        maxConcurrent: 15
      });

      const servers: EnhancedSyncServer[] = discovered.map(server => ({
        ip: server.ip,
        port: server.port,
        url: server.url,
        responseTime: server.responseTime,
        lastSeen: server.lastSeen
      }));

      // Sort servers by performance and preference
      const sortedServers = this.sortServersByPerformance(servers);

      this.status.discoveredServers = sortedServers;
      this.status.lastDiscovery = new Date();

      console.log(`🤖 [AutonomousSync] Discovered ${servers.length} servers (${discovered.filter(s => s.responseTime).length} cached):`, 
        servers.map(s => `${s.url} (${s.responseTime || 'new'}ms)`));

      // Auto-connect to discovered servers
      await this.autoConnectToServers(sortedServers);

      return sortedServers;
    } catch (error) {
      console.error('🤖 [AutonomousSync] Discovery failed:', error);
      return [];
    } finally {
      this.status.isDiscovering = false;
      this.emitStatusUpdate();
    }
  }

  private sortServersByPerformance(servers: EnhancedSyncServer[]): EnhancedSyncServer[] {
    return servers.sort((a, b) => {
      // 1. Preferred servers first
      const aPreferred = this.config.preferredServers.includes(a.ip);
      const bPreferred = this.config.preferredServers.includes(b.ip);
      
      if (aPreferred && !bPreferred) return -1;
      if (!aPreferred && bPreferred) return 1;
      
      // 2. Then by response time (faster first)
      if (a.responseTime && b.responseTime) {
        return a.responseTime - b.responseTime;
      }
      
      // 3. Servers with response time data first
      if (a.responseTime && !b.responseTime) return -1;
      if (!a.responseTime && b.responseTime) return 1;
      
      // 4. Then by port (5984 first)
      if (a.port === 5984 && b.port !== 5984) return -1;
      if (a.port !== 5984 && b.port === 5984) return 1;
      
      // 5. Finally by IP (localhost first)
      if (a.ip === '127.0.0.1' && b.ip !== '127.0.0.1') return -1;
      if (a.ip !== '127.0.0.1' && b.ip === '127.0.0.1') return 1;
      
      return 0;
    });
  }

  private async autoConnectToServers(servers: EnhancedSyncServer[]): Promise<void> {
    if (servers.length === 0) {
      console.log('🤖 [AutonomousSync] No servers to connect to');
      return;
    }

    // Skip if already connected to a server
    if (nativeSyncService.isConnected()) {
      console.log('🤖 [AutonomousSync] Already connected to a server, skipping auto-connect');
      return;
    }

    // Sort servers by preference
    const sortedServers = this.sortServersByPreference(servers);
    
    // Try to connect to the first available server
    for (const server of sortedServers) {
      // Skip servers that have failed too many times
      const failedServer = this.status.failedServers.find(f => f.server.url === server.url);
      if (failedServer && failedServer.attempts >= this.config.maxReconnectAttempts) {
        console.log(`🤖 [AutonomousSync] Skipping ${server.url} - max attempts reached`);
        continue;
      }

      console.log(`🤖 [AutonomousSync] Attempting to connect to ${server.url}...`);
      
      const success = await nativeSyncService.startSync(server, {
        live: true,
        retry: true
      });

      if (success) {
        console.log(`🤖 [AutonomousSync] Successfully connected to ${server.url}`);
        this.status.connectedServers = [server];
        this.removeFromFailedServers(server);
        this.emitStatusUpdate();
        break;
      } else {
        console.log(`🤖 [AutonomousSync] Failed to connect to ${server.url}`);
        this.addToFailedServers(server);
      }
    }
  }

  private sortServersByPreference(servers: EnhancedSyncServer[]): EnhancedSyncServer[] {
    return servers.sort((a, b) => {
      // Preferred servers first
      const aPreferred = this.config.preferredServers.includes(a.ip);
      const bPreferred = this.config.preferredServers.includes(b.ip);
      
      if (aPreferred && !bPreferred) return -1;
      if (!aPreferred && bPreferred) return 1;
      
      // Then by port (5984 first)
      if (a.port === 5984 && b.port !== 5984) return -1;
      if (a.port !== 5984 && b.port === 5984) return 1;
      
      return 0;
    });
  }

  private startPeriodicDiscovery(): void {
    if (this.discoveryTimer) {
      clearInterval(this.discoveryTimer);
    }

    this.discoveryTimer = setInterval(async () => {
      if (this.status.isRunning) {
        console.log('🤖 [AutonomousSync] Periodic discovery...');
        await this.performDiscovery();
      }
    }, this.config.discoveryInterval);
  }

  private startReconnectionMonitoring(): void {
    if (this.reconnectTimer) {
      clearInterval(this.reconnectTimer);
    }

    this.reconnectTimer = setInterval(async () => {
      if (this.status.isRunning && !nativeSyncService.isConnected()) {
        console.log('🤖 [AutonomousSync] Connection lost, attempting reconnection...');
        
        // Try to reconnect to known servers
        if (this.status.discoveredServers.length > 0) {
          await this.autoConnectToServers(this.status.discoveredServers);
        } else {
          // No known servers, perform discovery
          await this.performDiscovery();
        }
      }
    }, this.config.reconnectInterval);
  }

  private restartTimers(): void {
    this.startPeriodicDiscovery();
    this.startReconnectionMonitoring();
  }

  private addToFailedServers(server: EnhancedSyncServer): void {
    const existing = this.status.failedServers.find(f => f.server.url === server.url);
    if (existing) {
      existing.attempts++;
      existing.lastAttempt = new Date();
    } else {
      this.status.failedServers.push({
        server,
        attempts: 1,
        lastAttempt: new Date()
      });
    }
    this.emitStatusUpdate();
  }

  private removeFromFailedServers(server: EnhancedSyncServer): void {
    this.status.failedServers = this.status.failedServers.filter(f => f.server.url !== server.url);
    this.emitStatusUpdate();
  }

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }
}

// Export singleton instance
export const autonomousSyncManager = new AutonomousSyncManager();
export type { AutonomousConfig, AutonomousStatus };