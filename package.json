{"$schema": "https://json.schemastore.org/package.json", "name": "bistro", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "build": "node scripts/build.js web", "build:web": "node scripts/build.js web", "build:static": "node scripts/build.js static", "build:electron": "node scripts/build.js electron", "build:mobile": "node scripts/build.js mobile", "build:safe": "GENERATE_SOURCEMAP=false NEXT_DISABLE_SOURCEMAPS=true node scripts/build.js web", "build:prod": "NODE_ENV=production GENERATE_SOURCEMAP=false NEXT_DISABLE_SOURCEMAPS=true node scripts/build.js web", "build:secure": "./scripts/safe-build.sh", "clean:sourcemaps": "node ./scripts/clean-sourcemaps.js", "build:clean": "npm run build && npm run clean:sourcemaps", "clean:electron": "rm -rf electron/dist electron/app out .next", "fix:electron": "node scripts/fix-electron-cache.js", "prepare:couchdb": "node scripts/prepare-couchdb.js", "verify:couchdb": "node scripts/verify-couchdb.js", "check:couchdb": "chmod +x scripts/check-couchdb-setup.sh && ./scripts/check-couchdb-setup.sh", "test:db-flow": "node scripts/test-db-flow.js", "couchdb:status": "node scripts/manage-couchdb.js status", "couchdb:stop": "node scripts/manage-couchdb.js stop", "couchdb:cleanup": "node scripts/manage-couchdb.js cleanup", "electron:static": "npm run fix:electron && npm run build:electron && cd electron && npm run electron:start:static", "electron:safe": "npm run fix:electron && npm run build:electron && cd electron && npm run electron:start:static", "electron:test": "npm run build:electron && cd electron && npm run electron:start", "electron:package": "npm run build:electron && cd electron && npm run electron:build", "test:static-export": "node scripts/test-static-export.js", "start": "NODE_ENV=production node server.js", "lint": "next lint", "electron:dev": "node scripts/start-electron-dev.js", "electron:dev:concurrent": "concurrently --kill-others-on-fail \"npm run dev\" \"wait-on http://localhost:3000 && cd electron && npm run watch\" \"wait-on http://localhost:3000 && cd electron && npm run electron:start-live\"", "electron:dev:safe": "concurrently --kill-others-on-fail --restart-tries=3 \"npm run dev\" \"wait-on http://localhost:3000 && cd electron && npm run electron:start-live\"", "electron:cleanup": "node scripts/cleanup-electron-dev.js", "electron:build": "npm run build:electron && cd electron && npm run electron:build", "electron:build:win": "npm run build:electron && cd electron && npm run electron:build:win", "electron:build:mac": "npm run build:electron && cd electron && npm run electron:build:mac", "electron:build:mac:dmg": "npm run build:electron && cd electron && npm run electron:build:mac:dmg", "electron:dist": "npm run build:electron && cd electron && npm run dist", "deploy:windows": "chmod +x scripts/deploy-windows-simple.sh && ./scripts/deploy-windows-simple.sh", "deploy:windows:simple": "chmod +x scripts/deploy-windows-simple.sh && ./scripts/deploy-windows-simple.sh", "deploy:windows:r2": "chmod +x scripts/deploy-windows-r2.sh && ./scripts/deploy-windows-r2.sh", "deploy:windows:quick": "chmod +x scripts/quick-deploy-windows.sh && ./scripts/quick-deploy-windows.sh", "deploy:windows:nodeps": "chmod +x scripts/deploy-windows-r2-nodeps.sh && ./scripts/deploy-windows-r2-nodeps.sh", "copy:release:windows": "chmod +x scripts/copy-release-files.sh && ./scripts/copy-release-files.sh", "deploy:macos": "chmod +x scripts/deploy-macos-dmg-only.sh && ./scripts/deploy-macos-dmg-only.sh", "deploy:macos:full": "chmod +x scripts/deploy-macos-local.sh && ./scripts/deploy-macos-local.sh", "upload:r2": "node scripts/upload-to-r2.js", "upload:r2:both": "node scripts/upload-both-to-r2.js", "cap:dev:android": "node scripts/capacitor-dev-android.js", "cap:build:android": "npm run build:mobile && npx cap sync android && npx cap build android", "cap:prod:android": "node scripts/capacitor-prod-android.js", "android:setup-signing": "chmod +x scripts/setup-android-signing.sh && ./scripts/setup-android-signing.sh", "android:release": "chmod +x scripts/build-android-release.sh && ./scripts/build-android-release.sh", "deploy:android": "chmod +x scripts/deploy-android-r2.sh && ./scripts/deploy-android-r2.sh", "cap:dev:ios": "node scripts/capacitor-dev-ios.js", "cap:build:ios": "npm run build:mobile && npx cap sync ios && npx cap build ios", "cap:prod:ios": "node scripts/capacitor-prod-ios.js", "dev:all": "concurrently \"npm run dev\" \"npm run electron:dev\" \"npm run cap:dev:android\"", "dev:all:ios": "concurrently \"npm run dev\" \"npm run electron:dev\" \"npm run cap:dev:ios\""}, "dependencies": {"@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/lib-storage": "^3.606.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@capacitor-community/electron": "^5.0.1", "@capacitor/android": "^7.2.0", "@capacitor/barcode-scanner": "^2.0.3", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capawesome/capacitor-app-update": "^7.0.1", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.2.4", "@types/bcryptjs": "^2.4.6", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcryptjs": "^3.0.2", "canvas": "^3.1.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "electron-rebuild": "^3.2.9", "emoji-mart": "^5.6.0", "express": "^5.1.0", "get-port": "^7.1.0", "googleapis": "^149.0.0", "i18next": "^25.0.2", "input-otp": "^1.4.1", "jose": "^6.0.10", "jsbarcode": "^3.11.6", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.469.0", "mongodb": "^6.16.0", "motion": "^12.5.0", "multicast-dns": "^7.2.5", "nano": "^10.1.4", "next": "^15.3.1", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "node-fetch": "^2.7.0", "node-thermal-printer": "^4.5.0", "pouchdb": "^9.0.0", "pouchdb-adapter-memory": "^9.0.0", "pouchdb-authentication": "^1.1.3", "pouchdb-browser": "^9.0.0", "pouchdb-core": "^9.0.0", "pouchdb-find": "^9.0.0", "pouchdb-http": "^6.0.2", "pouchdb-node": "^9.0.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.1", "react-resizable-panels": "^2.1.7", "react-to-print": "^3.0.6", "recharts": "^2.15.1", "simple-peer": "^9.11.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "usb-detection": "^4.14.2", "uuid": "^11.1.0", "ws": "^8.18.1", "zod": "^3.24.1"}, "devDependencies": {"@aws-sdk/node-http-handler": "^3.370.0", "@shadcn/ui": "^0.0.4", "@stagewise/toolbar-next": "^0.5.1", "@types/aws-sdk": "^0.0.42", "@types/json-schema": "^7.0.15", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/multicast-dns": "^7.2.4", "@types/nano": "^6.4.6", "@types/node": "^20", "@types/pouchdb": "^6.4.2", "@types/pouchdb-browser": "^6.1.5", "@types/pouchdb-find": "^7.3.3", "@types/react": "^19", "@types/react-dom": "^19", "@types/socket.io": "^3.0.1", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "axios": "^1.9.0", "concurrently": "^9.1.2", "dotenv": "^16.5.0", "electron": "^36.7.1", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "ignore-loader": "^0.1.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-eslint": "^8.33.0", "wait-on": "^8.0.3", "workbox-build": "^7.3.0", "workbox-cacheable-response": "^7.3.0", "workbox-core": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-webpack-plugin": "^7.3.0", "workbox-window": "^7.3.0"}}