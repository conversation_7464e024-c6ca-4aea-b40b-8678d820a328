'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Search,
  Plus,
  UsersRound,
  CalendarDays,
  UserCheck,
  DollarSign,
  Loader2,
  RefreshCw,
  ArrowRight,
  Edit,
  Trash,
  UserPlus,
  AlertTriangle,
  UserCog,
  Wallet
} from 'lucide-react';
import { format, startOfMonth } from 'date-fns';
import { cn } from "@/lib/utils";
import { useAuth } from '@/lib/context/multi-user-auth-provider';
import { staffService } from '@/lib/services/staff-service';
import { StaffRole, ShiftConfig, StaffMember, PaymentConfig as LibraryPaymentConfig, Payment, PaymentType as LibraryPaymentType, StaffSchedule } from '@/lib/types/staff';
// Update the AppPaymentType to match library types to avoid type conflicts
type AppPaymentType = 'MONTHLY' | 'WEEKLY' | 'PER_SHIFT';
import ShiftManager from '@/app/components/staff/ShiftManager';
import { WeeklyScheduleGrid } from "@/app/components/staff/WeeklyScheduleGrid";
import { ShiftAttendance } from "@/app/components/staff/ShiftAttendance";
import { PersonnelForm } from "@/components/staff/PersonnelForm";
import { v4 as uuidv4 } from 'uuid';
import { initializeV4Database } from '@/lib/db/v4';
import { getCurrentRestaurantId } from '@/lib/db/v4/utils/restaurant-id';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { PaymentHistory } from "@/components/staff/PaymentHistory";
import { StaffPaymentSystem } from "@/components/staff/payment/StaffPaymentSystem";
import { SimpleTabPermissionGuard } from '@/components/auth/SimpleTabPermissionGuard';
import { SimpleStaffForm } from "./components/SimpleStaffForm";
import { 
  getAllStaff, 
  getStaffMember, 
  updateStaffMember, 
  deleteStaffMember,
  addStaffMember,
  getStaffSchedule,
  setStaffSchedule,
  recordStaffAttendance,
  getStaffAttendanceRecords,
  StaffDocument,
  ScheduleDocument,
  AttendanceRecord
} from '@/lib/db/v4';
import { isOnline } from '@/lib/utils/environment';

import { StaffAuthForm } from './components/StaffAuthForm';
import { usePermissions } from '@/lib/hooks/use-permissions';
import type { StaffMember as BaseStaffMember } from '@/lib/types/staff';

// Add defaultPermissions object at the top
const defaultPermissions = {
  pages: {
    menu: false,
    orders: false,
    finance: false,
    inventory: false,
    staff: false,
    settings: false,
    suppliers: false,
  }
};

// Adapter functions for type conversion - rewritten for clarity and accuracy
const adaptLibraryToAppPaymentType = (libType: LibraryPaymentType): AppPaymentType => {
  // Use a direct mapping without normalization
  if (libType === 'PER_SHIFT') return 'PER_SHIFT';
  if (libType === 'WEEKLY') return 'WEEKLY';
  if (libType === 'MONTHLY') return 'MONTHLY';

  console.warn(`Unknown payment type: ${libType}, defaulting to MONTHLY`);
  return 'MONTHLY';
};

const adaptAppToLibraryPaymentType = (appType: AppPaymentType): LibraryPaymentType => {
  // Use a direct mapping without normalization
  if (appType === 'PER_SHIFT') return 'PER_SHIFT';
  if (appType === 'WEEKLY') return 'WEEKLY';
  if (appType === 'MONTHLY') return 'MONTHLY';

  console.warn(`Unknown payment type: ${appType}, defaulting to MONTHLY`);
  return 'MONTHLY';
};

// Provide backward-compat alias that includes optional startDate and string role
type LibraryStaffMember = BaseStaffMember & {
  startDate?: string;
  role: string;
};

// Create proper instance of Staff interface from StaffMember
const adaptStaffMemberToStaff = (staffMember: LocalStaffMember): Staff => {
  return {
    id: staffMember.id,
    name: staffMember.name,
    role: staffMember.role as StaffRole,
    email: staffMember.email || '',
    phone: staffMember.phone || '',
    status: staffMember.status,
    startDate: (staffMember as any).startDate || staffMember.createdAt || new Date().toISOString(),
    paymentConfig: {
      type: staffMember.paymentConfig.type as AppPaymentType,
      baseSalary: staffMember.paymentConfig.baseSalary,
      shiftRate: staffMember.paymentConfig.shiftRate,
      shiftRates: staffMember.paymentConfig.shiftRates
    },
    presence: {
      staffId: staffMember.id,
      isPresent: false,
      attendanceHistory: [],
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0
    },
    userId: staffMember.userId || '',
    hasUserAccount: staffMember.hasUserAccount || false,
    shift: staffMember.shift ? adaptShiftConfigToShift(staffMember.shift) : undefined,
    schedule: staffMember.schedule,
    type: 'staff' // Add the required type field
  };
};

// Convert StaffDocument to StaffMember for UI compatibility
const convertStaffDocumentToStaffMember = (doc: StaffDocument): LocalStaffMember => {
  console.log(`🔍 DEBUG: Converting StaffDocument to StaffMember for ${doc.name}:`, {
    hasSchedule: !!(doc as any).schedule,
    scheduleStructure: (doc as any).schedule ? Object.keys((doc as any).schedule) : 'no schedule',
    scheduleData: (doc as any).schedule
  });
  
  const converted = {
    id: doc.id,
    name: doc.name,
    role: doc.role as StaffRole,
    contact: doc.contact,
    email: doc.email,
    phone: doc.phone,
    status: doc.status,
    paymentConfig: doc.paymentConfig,
    userId: doc.userId,
    hasUserAccount: doc.hasUserAccount,
    username: doc.username,
    pendingAuth: doc.pendingAuth,
    permissions: doc.permissions,
    createdAt: doc.createdAt,
    updatedAt: doc.updatedAt,
    // Add legacy properties with defaults
    startDate: doc.createdAt || new Date().toISOString(),
    presence: {
      staffId: doc.id,
      isPresent: false,
      attendanceHistory: [],
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0
    },
    shift: undefined,
    // 🔧 CRITICAL: Preserve the schedule data exactly as it comes from the service
    schedule: (doc as any).schedule
  };
  
  console.log(`✅ DEBUG: Converted ${doc.name} - schedule preserved:`, {
    hasSchedule: !!converted.schedule,
    scheduleStructure: converted.schedule ? Object.keys(converted.schedule) : 'no schedule',
    scheduleData: converted.schedule
  });
  
  return converted;
};

// Convert StaffMember to LibraryStaffMember for components
const adaptStaffMemberToLibraryStaffMember = (staffMember: LocalStaffMember): LibraryStaffMember => {
  return {
    id: staffMember.id,
    name: staffMember.name,
    role: staffMember.role as StaffRole,
    email: staffMember.email || '',
    phone: staffMember.phone || '',
    status: staffMember.status,
    startDate: (staffMember as any).startDate || staffMember.createdAt || new Date().toISOString(),
    paymentConfig: {
      type: staffMember.paymentConfig.type as LibraryPaymentType,
      baseSalary: staffMember.paymentConfig.baseSalary,
      shiftRate: staffMember.paymentConfig.shiftRate,
      shiftRates: staffMember.paymentConfig.shiftRates,
      paymentDay: staffMember.paymentConfig.paymentDay
    },
    presence: staffMember.presence || {
      staffId: staffMember.id,
      isPresent: false,
      attendanceHistory: [],
      totalHoursThisWeek: 0,
      totalHoursThisMonth: 0
    },
    schedule: staffMember.schedule,
    shift: staffMember.shift,
    _id: (staffMember as any)._id,
    _rev: (staffMember as any)._rev,
    type: 'staff',
    userId: staffMember.userId,
    hasUserAccount: staffMember.hasUserAccount || false,
    permissions: staffMember.permissions
  };
};

// Define a proper function to convert ShiftConfig to Shift
const adaptShiftConfigToShift = (config: ShiftConfig): Shift => {
  return {
    id: config.id,
    name: config.name,
    startTime: config.startTime,
    duration: config.endTime && config.startTime ?
      (parseInt(config.endTime.split(':')[0]) - parseInt(config.startTime.split(':')[0])) : 8,
    isActive: true
  };
};

// Use the main StaffMember type but create a local alias for this component's specific needs
type LocalStaffMember = StaffMember & {
  role: string; // Allow string for backwards compatibility with StaffDocument
  startDate?: string;
  presence?: any;
  shift?: any;
  schedule?: any;
};

// Define necessary interfaces directly in the file
interface Shift {
  id: string;
  name: string;
  startTime: string;
  duration: number;
  isActive: boolean;
}

interface Staff {
  id: string;
  name: string;
  role: StaffRole;
  email: string;
  phone: string;
  status: 'ACTIVE' | 'INACTIVE';
  startDate: string;
  paymentConfig: {
    type: AppPaymentType;
    baseSalary: number;
    shiftRate?: number;
    shiftRates?: Record<string, number>;
  };
  presence: {
    staffId: string;
    isPresent: boolean;
    attendanceHistory: any[];
    totalHoursThisWeek: number;
    totalHoursThisMonth: number;
  };
  userId: string;
  hasUserAccount?: boolean;
  shift?: Shift;
  schedule?: any;
  type: 'staff';
}

// Function to map our components to accept adapted types
const createAdapter = <T extends React.FC<any>>(Component: T) => {
  return Component as unknown as React.FC<any>;
};

// Create adapters for each component
const WeeklyScheduleGridAdapter = createAdapter(WeeklyScheduleGrid);
const ShiftManagerAdapter = createAdapter(ShiftManager);
const ShiftAttendanceAdapter = createAdapter(ShiftAttendance);

// Utility function to render role badges
const renderRoleBadge = (role: StaffRole) => {
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";

  switch (role) {
    case 'MANAGER':
      variant = "destructive";
      break;
    case 'CHEF':
    case 'WAITER':
      variant = "default";
      break;
    case 'DELIVERY':
      variant = "outline";
      break;
    default:
      variant = "secondary";
  }

  return (
    <Badge variant={variant}>
      {role.replace('_', ' ')}
    </Badge>
  );
};

// Add a helper function to calculate active permissions
const getActivePermissionCount = (staff: StaffMember) => {
  if (!staff.permissions || !staff.permissions.pages) return 0;
  return Object.values(staff.permissions.pages).filter(Boolean).length;
};

// Define AllStaff as needed for the component
type AllStaff = any; // This is a placeholder - replace with actual type if needed

// Extended Staff data for form submission that includes app access information
interface StaffFormData extends Omit<AllStaff, 'id'> {
  hasAppAccess?: boolean;
  appAccessCredentials?: {
    username: string;
    password: string;
  };
}

// Add App Access Dialog Component
interface AddAppAccessDialogProps {
  staffId: string;
  staffName: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

function AddAppAccessDialog({ staffId, staffName, isOpen, onClose, onSuccess }: AddAppAccessDialogProps) {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      toast({
        title: "Erreur",
        description: "Le nom d'utilisateur et le mot de passe sont obligatoires",
        variant: "destructive",
      });
      return;
    }

    if (password.length < 8) {
      toast({
        title: "Erreur",
        description: "Le mot de passe doit comporter au moins 8 caractères",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Get the JWT token from localStorage for authentication
      const token = localStorage.getItem('auth_token');
      if (!token) {
        console.error("No authentication token found");
        throw new Error("Authentication token not found - please refresh the page and try again");
      }

      // Default permissions - all set to false
      const defaultPermissions = {
        pages: {
          menu: false,
          orders: false,
          finance: false,
          inventory: false,
          staff: false,
          settings: false,
          suppliers: false
        }
      };

      console.log("Creating staff app access using staff-auth-service:", {
        staffId,
        username,
        password: password ? "[REDACTED]" : "[MISSING]"
      });

      // Get the staff member from the database
      const staffMember = await getStaffMember(staffId);

      if (!staffMember) {
        throw new Error(`Staff member with ID ${staffId} not found`);
      }

      // Use the new staff-auth-service to add auth credentials
      const { createStaffAuth } = await import('@/lib/services/staff-auth-service');
      const authResult = await createStaffAuth(
        staffMember,
        {
          username,
          password
        }
      );

      if (!authResult.success) {
        throw new Error(authResult.error || 'Failed to create authentication');
      }

      console.log("Staff auth creation successful", {
        staffId,
        userId: authResult.userId
      });

      toast({
        title: "Succès",
        description: "Accès à l'application créé avec succès",
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error creating app access:", error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Failed to create app access",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Ajouter un accès à l'application pour {staffName}</DialogTitle>
          <DialogDescription>
            Créez des identifiants de connexion pour ce membre du personnel afin d'accéder à l'application.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="username" className="text-right">
                Nom d'utilisateur
              </Label>
              <Input
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="col-span-3"
                placeholder="jean_dupont"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Mot de passe
              </Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="col-span-3"
                placeholder="••••••••"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Créer l'accès
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default function StaffManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterByRole, setFilterByRole] = useState('all');
  const [staffList, setStaffList] = useState<StaffMember[]>([]);
  const [shifts, setShifts] = useState<ShiftConfig[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPaymentHistoryOpen, setIsPaymentHistoryOpen] = useState(false);
  const [isAddAppAccessOpen, setIsAddAppAccessOpen] = useState(false);
  const [isAuthFormOpen, setIsAuthFormOpen] = useState(false);
  const [selectedStaffForAuth, setSelectedStaffForAuth] = useState<StaffMember | null>(null);
  const [selectedStaffForPayment, setSelectedStaffForPayment] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("staff");
  const { isAuthenticated, user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const { permissions, isOwner } = usePermissions();
  const staffTabPerms = isOwner 
    ? { 
        shifts_schedule: true, 
        attendance: true, 
        payments: true 
      }
    : permissions.tabs?.staff || {};

  // Form state
  const [name, setName] = useState('');
  const [role, setRole] = useState<StaffRole>('WAITER');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [salary, setSalary] = useState('');
  const [status, setStatus] = useState<'ACTIVE' | 'INACTIVE'>('ACTIVE');
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [paymentType, setPaymentType] = useState<AppPaymentType>('MONTHLY');
  const [shiftRate, setShiftRate] = useState('');
  const [paymentHistory, setPaymentHistory] = useState<Payment[]>([]);

  // Add these state variables at the beginning where other state is defined
  const [isAddingStaff, setIsAddingStaff] = useState(false);
  const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState<boolean>(false);
  const [selectedStaffForHistory, setSelectedStaffForHistory] = useState<StaffMember | null>(null);

  // Keep track of online status
  const [isOnlineState, setIsOnlineState] = useState(true);
  
  // Update online status
  useEffect(() => {
    // Initialize
    setIsOnlineState(isOnline());
    
    // Setup listeners
    const handleOnline = () => setIsOnlineState(true);
    const handleOffline = () => setIsOnlineState(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Update the refreshStaff method
  const refreshStaff = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Refreshing staff with schedules');
      
      // Use the enhanced service that loads schedules separately
      const staffWithSchedules = await staffService.getStaffWithSchedules();
      
      console.log(`✅ Refreshed ${staffWithSchedules.length} staff members with schedules`);
      setStaffList(staffWithSchedules);
    } catch (error) {
      console.error('Error refreshing staff:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données du personnel",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Use this same approach in useEffect for initial data loading
  useEffect(() => {
    const loadStaffData = async () => {
      if (!isAuthenticated) {
        setStaffList([]);
        return;
      }

      try {
        setIsLoading(true);

        // Initialize the v4 database with the authenticated restaurant ID
        try {
          // Get the authenticated restaurant ID
          const restaurantId = getCurrentRestaurantId();

          if (!restaurantId) {
            console.error('Staff Page - No authenticated restaurant ID found');
            setIsLoading(false);
            return; // Exit early - we can't proceed without a valid restaurant ID
          }

          console.log(`Staff Page - Initializing v4 database with authenticated restaurant ID: ${restaurantId}`);

          // Initialize the database with a retry mechanism
          let success = false;
          let retryCount = 0;
          const maxRetries = 3;

          while (!success && retryCount < maxRetries) {
            try {
              await initializeV4Database(restaurantId);
              success = true;
              if (success) {
                console.log('Staff Page - Successfully initialized v4 database');
              }
            } catch (err) {
              retryCount++;
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }

          if (!success) {
            console.error('Staff Page - Failed to initialize v4 database after multiple retries');
            setIsLoading(false);
            return; // Exit early - we can't proceed if initialization failed
          }
        } catch (initError) {
          console.error('Error initializing v4 database:', initError);
          setIsLoading(false);
          return; // Exit early - we can't proceed if initialization failed
        }

        // 🔧 FIXED: Use the main staffService with proper schedule loading
        console.log('📊 Loading staff with schedules using fixed staff service');
        const staffWithSchedules = await staffService.getStaffWithSchedules();

        // Convert to app format with proper permissions
        const adaptedStaff = staffWithSchedules.map((staffMember: any) => {
          const adapted = convertStaffDocumentToStaffMember(staffMember);

          // Ensure permissions are properly structured
          if (!adapted.permissions) {
            adapted.permissions = JSON.parse(JSON.stringify(defaultPermissions));
          }

          return adapted;
        });

        console.log(`✅ Loaded ${adaptedStaff.length} staff members with schedules:`, 
                   adaptedStaff.map(s => ({ 
                     id: s.id, 
                     name: s.name, 
                     hasSchedule: !!s.schedule,
                     scheduleData: s.schedule 
                   })));
        setStaffList(adaptedStaff);
      } catch (error) {
        console.error('Error loading staff data:', error);
        setErrorMessage("Failed to load staff data");
        toast({
          title: "Erreur",
          description: "Impossible de charger les données du personnel",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadStaffData();
  }, [isAuthenticated, toast]);

  // Load shifts data
  useEffect(() => {
    async function loadShifts() {
      if (!isAuthenticated) {
        setShifts([]);
        return;
      }
      // Initialize the v4 database for shifts
      try {
        const restaurantId = getCurrentRestaurantId();
        if (!restaurantId) {
          console.error('Staff Page - No authenticated restaurant ID found for shifts');
          setShifts([]);
          return;
        }
        console.log(`Staff Page - Initializing v4 database for shifts with restaurant ID: ${restaurantId}`);
        await initializeV4Database(restaurantId);
      } catch (initErr) {
        console.error('Staff Page - Failed to initialize v4 database for shifts:', initErr);
        setShifts([]);
        return;
      }

      try {
        // 🔧 FIXED: Use the main staff service directly
        console.log('📅 Loading shifts using main staff service');
        const shiftData = await staffService.getAllShifts();
        console.log(`📅 Loaded ${shiftData.length} shifts:`, shiftData);
        setShifts(shiftData);
      } catch (error) {
        console.error('Error loading shifts:', error);
        setShifts([]);
      }
    }

    loadShifts();
  }, [isAuthenticated]);

  // Debug logging for component data
  useEffect(() => {
    console.log('🔍 Debug - Component data update:', {
      activeTab,
      staffCount: staffList.length,
      shiftsCount: shifts.length,
      shiftDetails: shifts.map(s => ({ id: s.id, name: s.name, startTime: s.startTime, endTime: s.endTime })),
      staffTabPerms,
      isAuthenticated
    });
    
    if (activeTab === 'shifts_schedule') {
      console.log('🔍 Debug - WeeklyScheduleGrid data:', {
        staffSample: staffList.slice(0, 2).map(s => ({ id: s.id, name: s.name, hasSchedule: !!s.schedule, scheduleData: s.schedule })),
        shiftsSample: shifts.slice(0, 2),
        adaptedStaffSample: staffList.slice(0, 2).map(adaptStaffMemberToStaff).map(s => ({ id: s.id, name: s.name, hasSchedule: !!s.schedule, scheduleData: s.schedule })),
        adaptedShiftsSample: shifts.slice(0, 2).map(adaptShiftConfigToShift),
        totalShiftsToPass: shifts.length,
        totalStaffToPass: staffList.length
      });
    }
    
    if (activeTab === 'attendance') {
      console.log('🔍 Debug - ShiftAttendance data:', {
        staffSample: staffList.slice(0, 2).map(s => ({ id: s.id, name: s.name, hasSchedule: !!s.schedule, scheduleData: s.schedule })),
        shiftsSample: shifts.slice(0, 2),
        adaptedStaffSample: staffList.slice(0, 2).map(adaptStaffMemberToStaff).map(s => ({ id: s.id, name: s.name, hasSchedule: !!s.schedule, scheduleData: s.schedule })),
        adaptedShiftsSample: shifts.slice(0, 2).map(adaptShiftConfigToShift),
        totalShiftsToPass: shifts.length,
        totalStaffToPass: staffList.length
      });
    }
  }, [activeTab, staffList, shifts, staffTabPerms, isAuthenticated]);

  // Filter staff based on search term and role
  const filteredStaffList = staffList.filter(staff =>
    (staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (staff.phone || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    staff.role.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterByRole === 'all' || staff.role === filterByRole)
  );

  // Reset form
  const resetForm = () => {
    setName('');
    setRole('WAITER');
    setEmail('');
    setPhone('');
    setSalary('');
    setPaymentType('MONTHLY');
    setShiftRate('');
    setStatus('ACTIVE');
    setStartDate(new Date());
  };

  // Set form for edit
  const setFormForEdit = async (staff: StaffMember) => {
    try {
      // Load the staff member's schedule separately
      console.log(`🔄 Loading schedule for staff ${staff.id} for editing`);
      let weeklySchedule: Record<string, string[]> = {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      };

      // Try to load schedule data using per-staff operations
      try {
        const schedule = await getStaffSchedule(staff.id);
        if (schedule?.weeklySchedule) {
          weeklySchedule = schedule.weeklySchedule;
          console.log(`✅ Loaded schedule for ${staff.name}:`, weeklySchedule);
        } else {
          console.log(`⚠️ No schedule found for ${staff.name}, using empty schedule`);
        }
      } catch (scheduleError) {
        console.warn(`⚠️ Failed to load schedule for ${staff.name}:`, scheduleError);
      }

      // Set form data
      setName(staff.name);
      setRole(staff.role as StaffRole);
      setEmail(staff.email || '');
      setPhone(staff.phone || '');
      setSalary(staff.paymentConfig.baseSalary.toString());
      // Fix the payment type conversion
      const paymentType = staff.paymentConfig.type;
      if (paymentType === 'DAILY') {
        setPaymentType('MONTHLY'); // Convert DAILY to MONTHLY as fallback
      } else {
        setPaymentType(paymentType as AppPaymentType);
      }
      setShiftRate(staff.paymentConfig.shiftRate?.toString() || '');
      setStatus(staff.status);
      setStartDate(new Date(staff.startDate || staff.createdAt || new Date()));
      
      // Create the enhanced staff object with schedule data for SimpleStaffForm
      const staffWithSchedule = {
        ...staff,
        weeklySchedule // Include the loaded schedule data
      };
      
      setSelectedStaff(staffWithSchedule);
      setIsEditDialogOpen(true);
    } catch (error) {
      console.error('Error setting form for edit:', error);
      toast({
        title: "Error",
        description: "Failed to load staff data for editing",
        variant: "destructive",
      });
    }
  };

  // Handle add staff
  const handleAddStaff = async (data: StaffFormData) => {
    setIsAddingStaff(true);
    try {
      // Check if online before attempting to create staff with auth
      if (!isOnline() && data.hasAppAccess) {
        toast({
          title: "Offline Mode Detected",
          description: "⚠️ You're currently offline. Staff authentication requires an internet connection. You can create a staff member without login credentials for now.",
          variant: "destructive",
        });
        setIsAddingStaff(false);
        return;
      }

      // Convert to library type
      const libraryPaymentType = adaptAppToLibraryPaymentType(data.paymentConfig.type);

      // CRITICAL FIX: Generate a single UUID that will be used for BOTH the auth document ID and staff document ID
      // This is the key to ensuring consistency between the two documents
      let staffId = uuidv4();
      console.log('STAFF CREATION: Generated UUID that will be used for both auth and staff documents:', staffId);

      // IMPORTANT: This UUID must be used as-is without any prefixes or modifications
      // It must be the exact same string for both the auth document and staff document

      // Use the unified-create API to create staff with auth credentials if needed
      let userId = null;
      let hasUserAccount = false;

      try {
        // Get the JWT token from localStorage for authentication
        const token = localStorage.getItem('auth_token');
        if (!token) {
          throw new Error('Authentication token not found');
        }

        // Prepare the request data for the unified-create API
        const requestData = {
          name: data.name,
          role: data.role,
          email: data.email || '',
          phone: data.phone || '',
          status: data.status,
          paymentConfig: {
            type: data.paymentConfig.type,
            baseSalary: Number(data.paymentConfig.baseSalary) || 0,
            ...(data.paymentConfig.type === 'PER_SHIFT' && { shiftRates: data.paymentConfig.shiftRates }),
          },
          schedule: data.schedule,
          permissions: data.permissions,
          // Add auth credentials if app access is enabled
          ...(data.hasAppAccess && data.appAccessCredentials ? {
            auth: {
              username: data.appAccessCredentials.username,
              password: data.appAccessCredentials.password
            }
          } : {})
        };

        console.log('Sending unified staff creation request:', JSON.stringify(requestData, null, 2));

        // Call the unified-create API
        const response = await fetch('/api/staff/unified-create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(requestData)
        });

        // Add defensive check for JSON content-type before parsing
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          // If we got HTML instead of JSON (like a 404 page or error page)
          console.error('Server returned non-JSON response:', { status: response.status, contentType });

          // Check if we're likely dealing with a network/offline issue
          if (!navigator.onLine) {
            throw new Error('🌐 You are offline. Please connect to the internet to create staff with authentication credentials.');
          }

          throw new Error('🚨 Server error: Received HTML instead of JSON. The API route may be unavailable or you might be offline.');
        }

        // Parse the response with safeguards against empty responses
        let result;
        try {
          const text = await response.text();
          // First check if we even got a response
          if (!text || text.trim() === '') {
            console.error('Server returned empty response');
            throw new Error('🚨 Server returned an empty response. You may be offline or the server may be unavailable.');
          }

          // Then try to parse it as JSON
          result = JSON.parse(text);
        } catch (parseError: any) {
          console.error('Failed to parse server response as JSON:', parseError);
          if (!navigator.onLine) {
            throw new Error('🌐 You are offline. Please connect to the internet to create staff with authentication credentials.');
          }
          throw new Error(`🚨 Server returned invalid JSON: ${parseError.message || 'Unknown parse error'}`);
        }

        if (!response.ok && !result.staffMember) {
          // Check if this is an offline error
          if (result.offlineError) {
            throw new Error(`${result.message || 'Internet connection required for staff authentication.'}`);
          }
          throw new Error(result.error || 'Failed to create staff member');
        }

        console.log('Unified staff creation successful:', result);

        // Use the ID from the API response
        staffId = result.staffMember.id;
        userId = result.staffMember.id; // Same ID for both auth and staff documents
        hasUserAccount = result.authCreated;

        // Check for offline error even with success: true
        if (result.offlineError) {
          console.warn('Staff created locally, but MongoDB auth failed due to network/offline issues');
          toast({
            title: "Staff Added - But Auth Failed",
            description: result.message || "Staff member created locally, but authentication setup failed because you're offline. You can set up authentication when you're back online.",
            variant: "destructive",
          });
        }
        // Only show a warning if auth creation was requested but not successful for other reasons
        else if (data.hasAppAccess && data.appAccessCredentials && !result.authCreated) {
          console.warn('Auth creation was requested but not successful');
          toast({
            title: "Warning",
            description: `Staff created but app access could not be set up: ${result.message || 'Unknown error'}`,
            variant: "destructive",
          });
        } else if (data.hasAppAccess && data.appAccessCredentials && result.authCreated) {
          console.log('Auth creation succeeded, staff has app access');
        }

        // Skip the rest of the staff creation since it was already done by the unified-create API
        toast({
          title: "Staff Added",
          description: `${data.name} has been added to your staff roster.`,
        });

        // Refresh the staff list
        refreshStaff();

        // Close the dialog
        setIsAddDialogOpen(false);
        return;
      } catch (error) {
        console.error('Error in unified staff creation:', error);

        // Check if the error message suggests offline/network issues
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const isOfflineError =
          errorMessage.includes('offline') ||
          errorMessage.includes('Internet connection') ||
          errorMessage.includes('network') ||
          errorMessage.includes('unreachable');

        if (data.hasAppAccess && data.appAccessCredentials && isOfflineError) {
          // Special handling for offline errors when attempting to create auth
          toast({
            title: "Internet Required for Auth",
            description: errorMessage,
            variant: "destructive",
          });
        } else {
          toast({
            title: "Error",
            description: `Failed to create staff: ${errorMessage}`,
            variant: "destructive",
          });
        }

        setIsAddingStaff(false);
        return;
      }

      // This code is no longer needed as we're using the unified-create API
      // which handles all aspects of staff creation including auth credentials
    } catch (error) {
      console.error("Error adding staff:", error);
      toast({
        title: "Error",
        description: "Failed to add staff member. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAddingStaff(false);
    }
  };

  // Handle update staff
  const handleUpdateStaff = async (data: StaffFormData) => {
    if (!selectedStaff) return;

    try {
      setIsLoading(true);
      console.log("Updating staff with data:", data);

      // Convert to library type for update
      const libraryPaymentType = adaptAppToLibraryPaymentType(data.paymentConfig.type);

      // First, update the simple staff record in the database
      // This includes all the needed data for the database
      const simpleStaffUpdate = {
        name: data.name,
        role: data.role,
        contact: data.phone, // Using phone as the primary contact
        status: data.status,
        // Include full payment configuration
        paymentConfig: {
          type: data.paymentConfig.type,
          baseSalary: data.paymentConfig.baseSalary,
          // Include shift rates if applicable
          ...(data.paymentConfig.type === 'PER_SHIFT' && {
            shiftRates: data.paymentConfig.shiftRates
          })
        },
        // Include the schedule
        schedule: data.schedule,
        // Preserve user account info
        userId: selectedStaff.userId || '',
        hasUserAccount: !!selectedStaff.userId,
        // Include permissions in the staff document
        permissions: data.permissions
      };

      // Update the simple staff member in the database first
      try {
        await staffService.updateStaffMember(selectedStaff.id, {
          ...simpleStaffUpdate,
          userId: selectedStaff.userId || ''
        });
        console.log("Database record updated successfully");
      } catch (dbError) {
        console.warn("Error updating database record:", dbError);
        // Continue despite database error - we'll still update the local records
      }

      // Create the full updates for the staff service
      const updates: Partial<LibraryStaffMember> = {
        name: data.name,
        role: data.role,
        email: data.email,
        phone: data.phone,
        status: data.status,
        paymentConfig: {
          type: libraryPaymentType,
          baseSalary: data.paymentConfig.baseSalary,
          // Pass through shiftRates if they exist
          ...(data.paymentConfig as any).shiftRates ?
            { shiftRates: (data.paymentConfig as any).shiftRates } :
            {}
        },
        // Ensure we include the schedule
        schedule: data.schedule,
        // Include permissions in the staff document
        permissions: data.permissions
      };

      // Update the staff member in the staff service
      await staffService.updateStaffMember(selectedStaff.id, updates);

      // Update the schedule specifically to ensure it's properly saved
      if (data.schedule?.weeklySchedule) {
        await staffService.updateStaffWeeklySchedule(selectedStaff.id, data.schedule.weeklySchedule);
      }

      toast({
        title: "Personnel mis à jour",
        description: `Les informations de ${data.name} ont été mises à jour.`,
      });

      // Refresh the staff list
      refreshStaff();

      // Close the dialog
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Error updating staff:", error);
      toast({
        title: "Erreur",
        description: "Failed to update staff member. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete staff
  const handleDeleteStaff = async () => {
    if (!selectedStaff) return;

    try {
      setIsLoading(true);

      // Delete directly from database to avoid potential service layer issues
      try {
        await staffService.deleteStaffMember(selectedStaff.id);
        console.log(`Staff member ${selectedStaff.id} deleted from staff service`);
      } catch (serviceError) {
        console.warn(`Error deleting staff member ${selectedStaff.id} from staff service:`, serviceError);
      }

      // Refresh the list from staff service
      const libStaff = await staffService.getAllStaff();
      const adaptedStaff = libStaff.map(convertStaffDocumentToStaffMember);
      setStaffList(adaptedStaff);

      setIsDeleteDialogOpen(false);
      setSelectedStaff(null);

      toast({
        title: "Succès",
        description: "Membre du personnel supprimé avec succès",
      });
    } catch (error) {
      console.error('Error deleting staff:', error);
      toast({
        title: "Erreur",
        description: "Impossible de supprimer le membre du personnel : " + (error instanceof Error ? error.message : String(error)),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle shift update
  const handleShiftsUpdate = async () => {
    console.log('🔄 Handling shifts update - refreshing shifts from database');
    try {
      // Get fresh shifts data from the proper staff service
      const { staffService } = await import('@/lib/services/staff-service');
      const shiftData = await staffService.getAllShifts();
      console.log(`🔄 Refreshed ${shiftData.length} shifts:`, shiftData);
      setShifts(shiftData);
      
      // Also trigger a staff list refresh in case schedules need updating
      await refreshStaff();
    } catch (error) {
      console.error('Error refreshing shifts:', error);
      // Fallback: try using the current service
      try {
        const fallbackShifts = await staffService.getAllShifts();
        setShifts(fallbackShifts);
      } catch (fallbackError) {
        console.error('Fallback shifts refresh also failed:', fallbackError);
      }
    }
  };

  // Handle schedule update
  const handleScheduleUpdate = useCallback(async () => {
    // Refresh staff list with updated schedules
    const libStaff = await staffService.getAllStaff();
    const adaptedStaff = libStaff.map(convertStaffDocumentToStaffMember);
    setStaffList(adaptedStaff);
  }, []);

  // Handle opening payment history dialog
  const handleOpenPaymentHistory = (staff: StaffMember) => {
    setSelectedStaffForHistory(staff);
    setIsHistoryDialogOpen(true);
  };

  const handleClosePaymentHistory = () => {
    setSelectedStaffForHistory(null);
    setIsHistoryDialogOpen(false);
  };

  // Function to manually sync data from CouchDB
  const forceResyncFromCouchDB = async () => {
    try {
      setIsLoading(true);
      toast({
        title: "Synchronisation...",
        description: "Rafraîchissement des données depuis la base de données...",
      });

      // Clear local storage staff data
      staffService.clearStaffLocalStorage();

      // Refresh database sync
      // await staffService.refreshSyncStatus(); // Commented out, does not exist

      // Wait a moment for sync to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reload staff data from freshly synced database
      const libStaff = await staffService.getAllStaff();
      const adaptedStaff = libStaff.map(convertStaffDocumentToStaffMember);
      setStaffList(adaptedStaff);

      toast({
        title: "Synchronisation terminée",
        description: "Les données du personnel ont été rafraîchies depuis CouchDB.",
      });
    } catch (error) {
      console.error("Error forcing data resync:", error);
      toast({
        title: "Erreur de synchronisation",
        description: "Échec de la synchronisation des données depuis CouchDB",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // ShiftManager is located at this path
  const ShiftManagerSection = () => (
    <div className="space-y-4">
      <ShiftManager onShiftsUpdate={handleShiftsUpdate} />
    </div>
  );

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Gestion du personnel</h1>
        <p className="text-muted-foreground mt-1">
          Gérez le personnel de votre restaurant
        </p>
        

      </div>

      {!isAuthenticated ? (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h2 className="text-lg font-medium mb-2">Authentification requise</h2>
            <p className="text-muted-foreground mb-4">Veuillez vous connecter pour voir et gérer le personnel</p>
          </div>
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="mb-4">
            <TabsTrigger value="staff" className="flex items-center">
              <UsersRound className="mr-2 h-4 w-4" />
              Liste du personnel
            </TabsTrigger>
            {staffTabPerms.shifts_schedule && (
              <TabsTrigger value="shifts_schedule" className="flex items-center">
                <CalendarDays className="mr-2 h-4 w-4" />
                Plannings & Horaires
              </TabsTrigger>
            )}
            {staffTabPerms.attendance && (
              <TabsTrigger value="attendance" className="flex items-center">
                <UserCheck className="mr-2 h-4 w-4" />
                Présence
              </TabsTrigger>
            )}
            {staffTabPerms.payments && (
              <TabsTrigger value="payments" className="flex items-center">
                <DollarSign className="mr-2 h-4 w-4" />
                Paiements
              </TabsTrigger>
            )}

          </TabsList>

          {/* Staff List Tab */}
          <TabsContent value="staff" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="relative w-full md:w-72">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher un membre du personnel..."
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <div className="flex space-x-2">
                <Select
                  value={filterByRole}
                  onValueChange={setFilterByRole}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filtrer par rôle" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les rôles</SelectItem>
                    <SelectItem value="MANAGER">Manager</SelectItem>
                    <SelectItem value="CHEF">Chef</SelectItem>
                    <SelectItem value="WAITER">Serveur</SelectItem>
                    <SelectItem value="BARTENDER">Barman</SelectItem>
                    <SelectItem value="HOST">Hôte</SelectItem>
                    <SelectItem value="KITCHEN_HELPER">Aide cuisine</SelectItem>
                    <SelectItem value="CASHIER">Caissier</SelectItem>
                    <SelectItem value="CLEANER">Agent d'entretien</SelectItem>
                    <SelectItem value="DELIVERY">Livreur</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={() => setIsAddDialogOpen(true)}
                  className="flex items-center gap-2"
                  disabled={!isOnlineState}
                  title={isOnlineState ? "Ajouter un nouveau membre" : "Impossible d'ajouter du personnel hors ligne"}
                >
                  <UserPlus size={16} />
                  <span>Ajouter</span>
                </Button>
              </div>
            </div>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Membres du personnel</CardTitle>
                <CardDescription>
                  Voir et gérer tout le personnel du restaurant
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nom</TableHead>
                      <TableHead>Rôle</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-10">
                          <div className="flex justify-center">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : !filteredStaffList.length ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-10">
                          <p className="text-muted-foreground">Aucun membre du personnel trouvé</p>
                          {searchTerm && (
                            <p className="text-sm text-muted-foreground mt-1">
                              Essayez d'ajuster vos critères de recherche
                            </p>
                          )}
                          {!staffList.length && !searchTerm && (
                            <Button
                              variant="outline"
                              className="mt-4"
                              onClick={() => setIsAddDialogOpen(true)}
                            >
                              <Plus className="mr-2 h-4 w-4" />
                              Ajouter votre premier membre
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredStaffList.map((staff) => (
                        <TableRow key={staff.id}>
                          <TableCell className="font-medium">{staff.name}</TableCell>
                          <TableCell>{renderRoleBadge(staff.role as StaffRole)}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div className="text-muted-foreground">{staff.phone}</div>
                              <div className="text-xs text-muted-foreground mt-1">
                                {(() => {
                                  // Log payment config for debugging
                                  console.log(`Staff ${staff.name} payment display:`, {
                                    type: staff.paymentConfig.type,
                                    baseSalary: staff.paymentConfig.baseSalary,
                                    shiftRate: staff.paymentConfig.shiftRate,
                                    hasShiftRates: !!staff.paymentConfig.shiftRates,
                                    shiftRatesCount: staff.paymentConfig.shiftRates ? Object.keys(staff.paymentConfig.shiftRates).length : 0
                                  });

                                  // Ensure type is normalized for comparison
                                  const type = String(staff.paymentConfig.type).trim().toUpperCase();

                                  if (type === 'PER_SHIFT') {
                                    // Per-shift payment display
                                    if (staff.paymentConfig.shiftRates && Object.keys(staff.paymentConfig.shiftRates).length > 0) {
                                      return `Multi-shift rates (${Object.keys(staff.paymentConfig.shiftRates).length} shifts)` +
                                        (staff.paymentConfig.baseSalary > 0 ? ` + ${staff.paymentConfig.baseSalary.toLocaleString()} DZD base` : '');
                                    } else {
                                      return `${staff.paymentConfig.shiftRate ? staff.paymentConfig.shiftRate.toLocaleString() : 0} DZD/shift` +
                                        (staff.paymentConfig.baseSalary > 0 ? ` + ${staff.paymentConfig.baseSalary.toLocaleString()} DZD base` : '');
                                    }
                                  } else if (type === 'WEEKLY') {
                                    // Weekly payment display
                                    return `${staff.paymentConfig.baseSalary.toLocaleString()} DZD/week`;
                                  } else {
                                    // Monthly payment display (default)
                                    return `${staff.paymentConfig.baseSalary.toLocaleString()} DZD/month`;
                                  }
                                })()}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={staff.status === 'ACTIVE' ? "outline" : "secondary"}>
                              {staff.status === 'ACTIVE' ? 'Actif' : 'Inactif'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            {staffTabPerms.payments && (
                                <Button variant="ghost" size="icon" title="Payer" onClick={() => {
                                    setSelectedStaffForPayment(staff.id);
                                    setActiveTab('payments');
                                }}>
                                    <Wallet className="h-4 w-4" />
                                </Button>
                            )}
                            <Button variant="ghost" size="icon" onClick={() => { setFormForEdit(staff); setIsEditDialogOpen(true); }}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            {isOnlineState && (
                               <Button variant="ghost" size="icon" onClick={() => {
                                 setSelectedStaffForAuth(staff);
                                 setIsAuthFormOpen(true);
                               }}>
                                <UserCog className="h-4 w-4" />
                              </Button>
                            )}
                            <Button variant="ghost" size="icon" className="text-destructive" onClick={() => { setSelectedStaff(staff); setIsDeleteDialogOpen(true); }}>
                              <Trash className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Combined Shifts & Schedule Tab */}
          <TabsContent value="shifts_schedule">
            <SimpleTabPermissionGuard page="staff" tab="shifts_schedule">
              <div className="space-y-6">
                <ShiftManagerSection />
                {(() => {
                  // 🔍 DEBUG: Check what data we're passing to WeeklyScheduleGrid
                  const adaptedStaff = staffList.map(adaptStaffMemberToStaff);
                  const adaptedShifts = shifts.map(adaptShiftConfigToShift);
                  
                  console.log('🔍 DEBUG: Staff data being passed to WeeklyScheduleGrid:', {
                    originalStaffCount: staffList.length,
                    adaptedStaffCount: adaptedStaff.length,
                    staffSample: staffList.slice(0, 2).map(s => ({
                      id: s.id,
                      name: s.name,
                      status: s.status,
                      hasSchedule: !!s.schedule,
                      scheduleStructure: s.schedule ? Object.keys(s.schedule) : 'no schedule',
                      scheduleData: s.schedule
                    })),
                    adaptedStaffSample: adaptedStaff.slice(0, 2).map(s => ({
                      id: s.id,
                      name: s.name,
                      status: s.status,
                      hasSchedule: !!s.schedule,
                      scheduleStructure: s.schedule ? Object.keys(s.schedule) : 'no schedule',
                      scheduleData: s.schedule
                    })),
                    shiftsCount: shifts.length,
                    adaptedShiftsCount: adaptedShifts.length,
                    shiftsSample: shifts.slice(0, 2),
                    adaptedShiftsSample: adaptedShifts.slice(0, 2)
                  });
                  
                  return (
                    <WeeklyScheduleGridAdapter
                      staff={adaptedStaff}
                      shifts={adaptedShifts}
                      onScheduleUpdate={handleScheduleUpdate}
                    />
                  );
                })()}
              </div>
            </SimpleTabPermissionGuard>
          </TabsContent>

          {/* Attendance Tab */}
          <TabsContent value="attendance">
            <SimpleTabPermissionGuard page="staff" tab="attendance">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Staff Attendance Management</h3>
                <p className="text-sm text-muted-foreground">
                  Track and manage staff attendance, clock in/out, and view attendance history.
                </p>
                
                {/* ShiftAttendance Component for Attendance Management */}
                <Card>
                  <CardContent className="p-6">
                    <ShiftAttendance 
                      staff={staffList.map(adaptStaffMemberToLibraryStaffMember)} 
                      shifts={shifts}
                      onAttendanceUpdate={refreshStaff}
                    />
                  </CardContent>
                </Card>
              </div>
            </SimpleTabPermissionGuard>
          </TabsContent>

          {/* Payments Tab */}
          <TabsContent value="payments" className="mx-auto w-full space-y-4">
            <SimpleTabPermissionGuard page="staff" tab="payments">
              <StaffPaymentSystem 
                staffList={staffList}
                selectedStaffId={selectedStaffForPayment}
              />
            </SimpleTabPermissionGuard>
          </TabsContent>
        </Tabs>
      )}

      {/* Add Staff Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-[1000px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Ajouter un membre du personnel</DialogTitle>
          </DialogHeader>
          <SimpleStaffForm
            onSuccess={(staffId) => {
              setIsAddDialogOpen(false);
              refreshStaff();
              toast({
                title: "Staff created",
                description: "New staff member has been added successfully",
              });
            }}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Staff Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-[1000px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Modifier le membre du personnel</DialogTitle>
          </DialogHeader>
          {selectedStaff && (
            <SimpleStaffForm
              initialData={selectedStaff}
              onSuccess={() => {
                setIsEditDialogOpen(false);
                refreshStaff();
                toast({
                  title: "Staff updated",
                  description: "Staff member has been updated successfully",
                });
              }}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Staff Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Supprimer un membre</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer {selectedStaff?.name} ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 mt-4">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={handleDeleteStaff}>
              Supprimer
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Payment History Dialog */}
      {selectedStaffForHistory && isPaymentHistoryOpen && (
        <Dialog open={isPaymentHistoryOpen} onOpenChange={setIsPaymentHistoryOpen}>
          <DialogContent className="max-w-[800px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Historique des paiements pour {selectedStaffForHistory?.name}</DialogTitle>
              <DialogDescription>
                Voir tous les paiements passés pour ce membre du personnel
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4">
              {paymentHistory.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  Aucun historique de paiement trouvé pour ce membre du personnel.
                </div>
              ) : (
                <PaymentHistory
                  staffId={selectedStaffForHistory!.id}
                  staffName={selectedStaffForHistory!.name}
                />
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Add App Access Dialog */}
      {selectedStaff && (
        <AddAppAccessDialog
          staffId={selectedStaff.id}
          staffName={selectedStaff.name}
          isOpen={isAddAppAccessOpen}
          onClose={() => setIsAddAppAccessOpen(false)}
          onSuccess={async () => {
            try {
              // Update the staff record to indicate it has a user account
              if (selectedStaff) {
                // Update the selected staff member in the service
                await staffService.updateStaffMember(selectedStaff.id, {
                  hasUserAccount: true
                } as Partial<LibraryStaffMember>);

                // Refresh the entire staff list from the service
                const libStaff = await staffService.getAllStaff();
                const adaptedStaff = libStaff.map(convertStaffDocumentToStaffMember);
                setStaffList(adaptedStaff);
              }
            } catch (error) {
              console.error("Error updating staff record:", error);
              // Still update the UI for better UX even if the backend update fails
              const updatedStaffList = staffList.map(staff =>
                staff.id === selectedStaff?.id
                  ? { ...staff, hasUserAccount: true }
                  : staff
              );
              setStaffList(updatedStaffList);
            }
          }}
        />
      )}

      {/* Auth Form Dialog - Separated from staff creation */}
      <Dialog open={isAuthFormOpen} onOpenChange={setIsAuthFormOpen}>
        <DialogContent className="max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {selectedStaffForAuth?.hasUserAccount ? 'Edit App Access' : 'Create App Access'}
            </DialogTitle>
            <DialogDescription>
              {selectedStaffForAuth?.hasUserAccount 
                ? 'Update authentication credentials for staff member to access the app'
                : 'Create authentication credentials for staff member to access the app'
              }
            </DialogDescription>
          </DialogHeader>
          {selectedStaffForAuth && (
            <StaffAuthForm
              staffMember={selectedStaffForAuth}
              onSuccess={() => {
                setIsAuthFormOpen(false);
                setSelectedStaffForAuth(null);
                refreshStaff();
                toast({
                  title: selectedStaffForAuth.hasUserAccount ? "Auth credentials updated" : "Auth credentials created",
                  description: `Login access has been ${selectedStaffForAuth.hasUserAccount ? 'updated' : 'created'} for ${selectedStaffForAuth.name}`,
                });
              }}
              onCancel={() => {
                setIsAuthFormOpen(false);
                setSelectedStaffForAuth(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Add a warning message when offline */}
      {!isOnlineState && (
        <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800">
          <div className="flex items-center gap-2">
            <AlertTriangle size={16} />
            <span className="font-medium">You're offline</span>
          </div>
          <p className="mt-1 text-sm">
            You can view staff members, but adding new staff or modifying authentication requires an internet connection.
          </p>
        </div>
      )}
    </div>
  );
}